"""
Test chi tiết các trường hợp validation format đề thi
"""

import requests
import json
from docx import Document
import io

def create_exam_docx(missing_elements=None):
    """
    Tạo file DOCX với các phần tử có thể thiếu
    
    Args:
        missing_elements: List các phần tử cần bỏ qua
    """
    if missing_elements is None:
        missing_elements = []
    
    doc = Document()
    
    # Header chuẩn
    if "header" not in missing_elements:
        doc.add_paragraph("BỘ GIÁO DỤC VÀ ĐÀO TẠO")
        doc.add_paragraph("Trường THPT Hong Thinh")
        doc.add_paragraph("ĐỀ KIỂM TRA LỚP 12")
        doc.add_paragraph("Môn: HOA HOC")
        doc.add_paragraph("Thời gian làm bài: 90 phút, không kể thời gian phát đề")
        doc.add_paragraph("")
    
    # Thông tin thí sinh
    if "student_info" not in missing_elements:
        doc.add_paragraph("<PERSON><PERSON>, tên thí sinh: ..................................................")
        doc.add_paragraph("Mã đề: 1234")
        doc.add_paragraph("Số báo danh: ......................................................")
        doc.add_paragraph("")
    
    # Bảng nguyên tử khối (chỉ cho môn Hóa)
    if "atomic_table" not in missing_elements:
        doc.add_paragraph("BẢNG NGUYÊN TỬ KHỐI CỦA CÁC NGUYÊN TỐ HÓA HỌC")
        doc.add_paragraph("H = 1; C = 12; N = 14; O = 16; S = 32")
        doc.add_paragraph("")
    
    # Phần I
    if "part1" not in missing_elements:
        doc.add_paragraph("PHẦN I. Câu trắc nghiệm nhiều phương án lựa chọn.")
        doc.add_paragraph("Câu 1. Hạt nào sau đây không cấu tạo nên hạt nhân nguyên tử?")
        doc.add_paragraph("A. Proton")
        doc.add_paragraph("B. Neutron") 
        doc.add_paragraph("C. Electron")
        doc.add_paragraph("D. Cả proton và neutron")
        doc.add_paragraph("")
    
    # Phần II
    if "part2" not in missing_elements:
        doc.add_paragraph("PHẦN II. Câu trắc nghiệm đúng sai.")
        doc.add_paragraph("Câu 1. Xét về cấu tạo nguyên tử:")
        if "true_false_format" not in missing_elements:
            doc.add_paragraph("a) Nguyên tử Hydrogen chỉ chứa một proton và một electron.")
            doc.add_paragraph("b) Hạt nhân của nguyên tử Hydrogen chứa cả proton và neutron.")
            doc.add_paragraph("c) Nguyên tử Hydrogen là một hạt trung hòa về điện.")
            doc.add_paragraph("d) Số proton trong hạt nhân của Hydrogen bằng số electron trong lớp vỏ nguyên tử.")
        doc.add_paragraph("")
    
    # Phần III
    if "part3" not in missing_elements:
        doc.add_paragraph("PHẦN III. Câu trắc nghiệm trả lời ngắn.")
        doc.add_paragraph("Câu 1. Tính khối lượng gần đúng của nguyên tử X theo đơn vị amu.")
        doc.add_paragraph("")
    
    # Đáp án
    if "answer_section" not in missing_elements:
        doc.add_paragraph("ĐÁP ÁN")
        doc.add_paragraph("PHẦN I: 1-C")
        doc.add_paragraph("PHẦN II: 1. a)Đúng b)Sai c)Đúng d)Đúng")
        doc.add_paragraph("PHẦN III: 1. 1")
    
    # Lưu vào memory
    file_stream = io.BytesIO()
    doc.save(file_stream)
    file_stream.seek(0)
    
    return file_stream.getvalue()

def test_validation_case(case_name, missing_elements, expected_error_keywords):
    """Test một trường hợp validation cụ thể"""
    print(f"\n=== Test Case: {case_name} ===")
    
    try:
        # Tạo file DOCX
        docx_content = create_exam_docx(missing_elements)
        print(f"Created DOCX file, size: {len(docx_content)} bytes")
        print(f"Missing elements: {missing_elements}")
        
        # Gửi request
        files = {
            'file': (f'{case_name.lower().replace(" ", "_")}.docx', docx_content, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
        }
        
        response = requests.post(
            "http://localhost:8000/api/v1/exam/import-docx",
            files=files,
            timeout=60
        )
        
        print(f"Status Code: {response.status_code}")
        result = response.json()
        
        # Kiểm tra kết quả
        if missing_elements:  # Expect failure
            if not result.get("success") and result.get("error_code") == "INVALID_FORMAT":
                error_message = result.get("error", "").lower()
                found_keywords = [kw for kw in expected_error_keywords if kw.lower() in error_message]
                
                if found_keywords:
                    print(f"✅ PASSED - Detected expected error: {found_keywords}")
                    print(f"Error: {result.get('error')}")
                    return True
                else:
                    print(f"❌ FAILED - Error detected but wrong type")
                    print(f"Expected keywords: {expected_error_keywords}")
                    print(f"Actual error: {result.get('error')}")
                    return False
            else:
                print(f"❌ FAILED - Should have failed but didn't")
                print(f"Result: {result}")
                return False
        else:  # Expect success
            if result.get("success"):
                print(f"✅ PASSED - Valid format processed successfully")
                return True
            else:
                print(f"❌ FAILED - Valid format rejected")
                print(f"Error: {result.get('error')}")
                return False
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    """Chạy tất cả test cases"""
    print("🧪 Testing Detailed Format Validation")
    print("=" * 60)
    
    test_cases = [
        {
            "name": "Valid Complete Format",
            "missing_elements": [],
            "expected_error_keywords": []
        },
        {
            "name": "Missing Header",
            "missing_elements": ["header"],
            "expected_error_keywords": ["header", "BỘ GIÁO DỤC"]
        },
        {
            "name": "Missing Student Info",
            "missing_elements": ["student_info"],
            "expected_error_keywords": ["thông tin", "HỌ, TÊN", "MÃ ĐỀ"]
        },
        {
            "name": "Missing Parts Structure",
            "missing_elements": ["part1", "part2", "part3"],
            "expected_error_keywords": ["PHẦN", "cấu trúc"]
        },
        {
            "name": "Missing Answer Section",
            "missing_elements": ["answer_section"],
            "expected_error_keywords": ["ĐÁP ÁN"]
        },
        {
            "name": "Missing Atomic Table (Chemistry)",
            "missing_elements": ["atomic_table"],
            "expected_error_keywords": ["nguyên tử khối", "Hóa học"]
        },
        {
            "name": "Missing True/False Format",
            "missing_elements": ["true_false_format"],
            "expected_error_keywords": ["a)", "b)", "c)", "d)"]
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        result = test_validation_case(
            test_case["name"],
            test_case["missing_elements"],
            test_case["expected_error_keywords"]
        )
        results.append((test_case["name"], result))
    
    # Kết quả tổng
    print("\n" + "=" * 60)
    print("📊 DETAILED TEST RESULTS:")
    
    passed = 0
    total = len(results)
    
    for name, result in results:
        status = "PASSED" if result else "FAILED"
        icon = "✅" if result else "❌"
        print(f"{icon} {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 Summary: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Format validation hoạt động hoàn hảo.")
    else:
        print(f"\n⚠️  {total-passed} TESTS FAILED! Cần kiểm tra lại validation logic.")

if __name__ == "__main__":
    main()

"""
Test cuối cùng để xác nhận validation đã được tối ưu đúng cách
"""

import requests
import json
from docx import Document
import io

def create_minimal_valid_exam():
    """Tạo đề thi tối thiểu nhưng hợp lệ"""
    doc = Document()
    
    # Không cần header phức tạp
    doc.add_paragraph("Đề thi test")
    doc.add_paragraph("")
    
    # Chỉ cần 3 phần
    doc.add_paragraph("PHẦN I")
    doc.add_paragraph("Câu 1. Test")
    doc.add_paragraph("")
    
    doc.add_paragraph("PHẦN II") 
    doc.add_paragraph("Câu 1. Test")
    doc.add_paragraph("")
    
    doc.add_paragraph("PHẦN III")
    doc.add_paragraph("Câu 1. Test")
    doc.add_paragraph("")
    
    # Chỉ cần đáp án
    doc.add_paragraph("ĐÁP ÁN")
    doc.add_paragraph("Test answers")
    
    file_stream = io.BytesIO()
    doc.save(file_stream)
    file_stream.seek(0)
    return file_stream.getvalue()

def create_exam_without_header():
    """Tạo đề thi không có header chuẩn nhưng có đủ phần"""
    doc = Document()
    
    # Không có header "BỘ GIÁO DỤC VÀ ĐÀO TẠO" etc
    doc.add_paragraph("Đề kiểm tra")
    doc.add_paragraph("")
    
    # Có đủ 3 phần
    doc.add_paragraph("PHẦN I")
    doc.add_paragraph("Câu 1. Test")
    doc.add_paragraph("")
    
    doc.add_paragraph("PHẦN II")
    doc.add_paragraph("Câu 1. Test") 
    doc.add_paragraph("")
    
    doc.add_paragraph("PHẦN III")
    doc.add_paragraph("Câu 1. Test")
    doc.add_paragraph("")
    
    # Có đáp án
    doc.add_paragraph("ĐÁP ÁN")
    doc.add_paragraph("Test answers")
    
    file_stream = io.BytesIO()
    doc.save(file_stream)
    file_stream.seek(0)
    return file_stream.getvalue()

def create_exam_without_atomic_table():
    """Tạo đề thi Hóa học không có bảng nguyên tử khối"""
    doc = Document()
    
    doc.add_paragraph("Đề thi môn HÓA HỌC")
    doc.add_paragraph("")
    
    # Không có "BẢNG NGUYÊN TỬ KHỐI"
    
    # Có đủ 3 phần
    doc.add_paragraph("PHẦN I")
    doc.add_paragraph("Câu 1. Test")
    doc.add_paragraph("")
    
    doc.add_paragraph("PHẦN II")
    doc.add_paragraph("Câu 1. Test")
    doc.add_paragraph("")
    
    doc.add_paragraph("PHẦN III") 
    doc.add_paragraph("Câu 1. Test")
    doc.add_paragraph("")
    
    # Có đáp án
    doc.add_paragraph("ĐÁP ÁN")
    doc.add_paragraph("Test answers")
    
    file_stream = io.BytesIO()
    doc.save(file_stream)
    file_stream.seek(0)
    return file_stream.getvalue()

def test_validation_optimization():
    """Test xem validation đã được tối ưu chưa"""
    print("🧪 Testing Validation Optimization")
    print("=" * 50)
    
    test_cases = [
        {
            "name": "Minimal Valid Exam",
            "content": create_minimal_valid_exam(),
            "should_pass_validation": True,
            "description": "Đề thi tối thiểu với 3 phần + đáp án"
        },
        {
            "name": "No Header But Valid Structure", 
            "content": create_exam_without_header(),
            "should_pass_validation": True,
            "description": "Không có header chuẩn nhưng có đủ cấu trúc"
        },
        {
            "name": "Chemistry Without Atomic Table",
            "content": create_exam_without_atomic_table(), 
            "should_pass_validation": True,
            "description": "Đề Hóa học không có bảng nguyên tử khối"
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n=== {test_case['name']} ===")
        print(f"Description: {test_case['description']}")
        
        try:
            files = {
                'file': (f'{test_case["name"].lower().replace(" ", "_")}.docx', 
                        test_case["content"], 
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
            }
            
            response = requests.post(
                "http://localhost:8000/api/v1/exam/import-docx",
                files=files,
                timeout=60
            )
            
            result = response.json()
            
            # Kiểm tra xem có pass validation không
            if result.get("error_code") == "INVALID_FORMAT":
                print(f"❌ FAILED - Bị từ chối ở validation: {result.get('error')}")
                passed = False
            else:
                print("✅ PASSED - Vượt qua validation (có thể fail ở bước sau)")
                passed = True
            
            results.append((test_case["name"], passed))
            
        except Exception as e:
            print(f"❌ ERROR: {e}")
            results.append((test_case["name"], False))
    
    # Kết quả
    print("\n" + "=" * 50)
    print("📊 OPTIMIZATION TEST RESULTS:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} {name}")
    
    print(f"\n📈 Summary: {passed}/{total} tests passed validation")
    
    if passed == total:
        print("\n🎉 VALIDATION OPTIMIZATION SUCCESSFUL!")
        print("✅ Validation chỉ kiểm tra cấu trúc cơ bản (3 phần + đáp án)")
        print("✅ Không yêu cầu header phức tạp")
        print("✅ Không yêu cầu bảng nguyên tử khối")
        print("✅ Tiết kiệm token LLM bằng cách từ chối sớm file sai format")
    else:
        print(f"\n⚠️ VALIDATION TOO STRICT!")
        print(f"Validation vẫn từ chối {total-passed} file hợp lệ")

if __name__ == "__main__":
    test_validation_optimization()

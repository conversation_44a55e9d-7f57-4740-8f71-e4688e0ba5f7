"""
Test script để kiểm tra validation format đề thi
"""

import requests
import json
from docx import Document
import io

def create_valid_exam_docx():
    """Tạo file DOCX với format đúng chuẩn"""
    doc = Document()
    
    # Header chuẩn
    doc.add_paragraph("BỘ GIÁO DỤC VÀ ĐÀO TẠO")
    doc.add_paragraph("Trường THPT Hong Thinh")
    doc.add_paragraph("ĐỀ KIỂM TRA LỚP 12")
    doc.add_paragraph("Môn: HOA HOC")
    doc.add_paragraph("Thời gian làm bài: 90 phút, không kể thời gian phát đề")
    doc.add_paragraph("")
    
    # Thông tin thí sinh
    doc.add_paragraph("Họ, tên thí sinh: ..................................................")
    doc.add_paragraph("Mã đề: 1234")
    doc.add_paragraph("Số báo danh: ......................................................")
    doc.add_paragraph("")
    
    # Bảng nguyên tử khối
    doc.add_paragraph("BẢNG NGUYÊN TỬ KHỐI CỦA CÁC NGUYÊN TỐ HÓA HỌC")
    doc.add_paragraph("H = 1; C = 12; N = 14; O = 16; S = 32")
    doc.add_paragraph("")
    
    # Phần I
    doc.add_paragraph("PHẦN I. Câu trắc nghiệm nhiều phương án lựa chọn.")
    doc.add_paragraph("Câu 1. Hạt nào sau đây không cấu tạo nên hạt nhân nguyên tử?")
    doc.add_paragraph("A. Proton")
    doc.add_paragraph("B. Neutron") 
    doc.add_paragraph("C. Electron")
    doc.add_paragraph("D. Cả proton và neutron")
    doc.add_paragraph("")
    
    # Phần II
    doc.add_paragraph("PHẦN II. Câu trắc nghiệm đúng sai.")
    doc.add_paragraph("Câu 1. Xét về cấu tạo nguyên tử:")
    doc.add_paragraph("a) Nguyên tử Hydrogen chỉ chứa một proton và một electron.")
    doc.add_paragraph("b) Hạt nhân của nguyên tử Hydrogen chứa cả proton và neutron.")
    doc.add_paragraph("c) Nguyên tử Hydrogen là một hạt trung hòa về điện.")
    doc.add_paragraph("d) Số proton trong hạt nhân của Hydrogen bằng số electron trong lớp vỏ nguyên tử.")
    doc.add_paragraph("")
    
    # Phần III
    doc.add_paragraph("PHẦN III. Câu trắc nghiệm trả lời ngắn.")
    doc.add_paragraph("Câu 1. Tính khối lượng gần đúng của nguyên tử X theo đơn vị amu.")
    doc.add_paragraph("")
    
    # Đáp án
    doc.add_paragraph("ĐÁP ÁN")
    doc.add_paragraph("PHẦN I: 1-C")
    doc.add_paragraph("PHẦN II: 1. a)Đúng b)Sai c)Đúng d)Đúng")
    doc.add_paragraph("PHẦN III: 1. 1")
    
    # Lưu vào memory
    file_stream = io.BytesIO()
    doc.save(file_stream)
    file_stream.seek(0)
    
    return file_stream.getvalue()

def create_invalid_exam_docx():
    """Tạo file DOCX với format sai"""
    doc = Document()
    
    # Header sai
    doc.add_paragraph("ĐỀ THI MÔN HÓA HỌC")  # Thiếu header chuẩn
    doc.add_paragraph("Lớp 12")
    doc.add_paragraph("")
    
    # Thiếu thông tin thí sinh
    doc.add_paragraph("Câu 1. Hạt nào sau đây không cấu tạo nên hạt nhân nguyên tử?")
    doc.add_paragraph("A. Proton")
    doc.add_paragraph("B. Neutron")
    
    # Lưu vào memory
    file_stream = io.BytesIO()
    doc.save(file_stream)
    file_stream.seek(0)
    
    return file_stream.getvalue()

def test_valid_format():
    """Test với format đúng chuẩn"""
    print("=== Test Valid Format ===")
    
    try:
        # Tạo file DOCX đúng format
        docx_content = create_valid_exam_docx()
        print(f"Created valid DOCX file, size: {len(docx_content)} bytes")
        
        # Gửi request
        files = {
            'file': ('valid_exam.docx', docx_content, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
        }
        
        response = requests.post(
            "http://localhost:8000/api/v1/exam/import-docx",
            files=files,
            timeout=60
        )
        
        print(f"Status Code: {response.status_code}")
        result = response.json()
        
        if result.get("success"):
            print("✅ Valid format test PASSED - File được xử lý thành công")
            print(f"Message: {result.get('message')}")
        else:
            print("❌ Valid format test FAILED - File đúng format nhưng bị reject")
            print(f"Error: {result.get('error')}")
            print(f"Error Code: {result.get('error_code')}")
        
        return result.get("success", False)
        
    except Exception as e:
        print(f"❌ Error in valid format test: {e}")
        return False

def test_invalid_format():
    """Test với format sai"""
    print("\n=== Test Invalid Format ===")
    
    try:
        # Tạo file DOCX sai format
        docx_content = create_invalid_exam_docx()
        print(f"Created invalid DOCX file, size: {len(docx_content)} bytes")
        
        # Gửi request
        files = {
            'file': ('invalid_exam.docx', docx_content, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
        }
        
        response = requests.post(
            "http://localhost:8000/api/v1/exam/import-docx",
            files=files,
            timeout=60
        )
        
        print(f"Status Code: {response.status_code}")
        result = response.json()
        
        if not result.get("success") and result.get("error_code") == "INVALID_FORMAT":
            print("✅ Invalid format test PASSED - File sai format bị reject đúng cách")
            print(f"Error: {result.get('error')}")
            print(f"Validation details: {result.get('details', {}).get('validation_details')}")
        else:
            print("❌ Invalid format test FAILED - File sai format không bị reject")
            print(f"Result: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        return not result.get("success", True) and result.get("error_code") == "INVALID_FORMAT"
        
    except Exception as e:
        print(f"❌ Error in invalid format test: {e}")
        return False

def main():
    """Chạy tất cả test cases"""
    print("🧪 Testing Exam Format Validation")
    print("=" * 50)
    
    # Test 1: Format đúng chuẩn
    valid_test_passed = test_valid_format()
    
    # Test 2: Format sai
    invalid_test_passed = test_invalid_format()
    
    # Kết quả tổng
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS:")
    print(f"✅ Valid format test: {'PASSED' if valid_test_passed else 'FAILED'}")
    print(f"✅ Invalid format test: {'PASSED' if invalid_test_passed else 'FAILED'}")
    
    if valid_test_passed and invalid_test_passed:
        print("\n🎉 ALL TESTS PASSED! Format validation hoạt động đúng.")
    else:
        print("\n❌ SOME TESTS FAILED! Cần kiểm tra lại validation logic.")

if __name__ == "__main__":
    main()

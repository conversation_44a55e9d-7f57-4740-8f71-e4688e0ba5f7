"""
Test validation đơn giản - chỉ kiểm tra 3 phần và đáp án
"""

import requests
import json
from docx import Document
import io

def create_valid_exam():
    """Tạo đề thi đúng format"""
    doc = Document()
    
    doc.add_paragraph("Đề thi môn <PERSON> học")
    doc.add_paragraph("")
    
    # 3 phần bắt buộc
    doc.add_paragraph("PHẦN I. Trắc nghiệm nhiều phương án")
    doc.add_paragraph("Câu 1. Câu hỏi test...")
    doc.add_paragraph("A. Đáp án A")
    doc.add_paragraph("B. Đáp án B")
    doc.add_paragraph("")
    
    doc.add_paragraph("PHẦN II. Trắc nghiệm đúng sai")
    doc.add_paragraph("Câu 1. Câu hỏi test...")
    doc.add_paragraph("a) Phát biểu a")
    doc.add_paragraph("b) Phát biểu b")
    doc.add_paragraph("")
    
    doc.add_paragraph("PHẦN III. Trắc nghiệm trả lời ngắn")
    doc.add_paragraph("Câu 1. Câu hỏi test...")
    doc.add_paragraph("")
    
    # Đáp án bắt buộc
    doc.add_paragraph("ĐÁP ÁN")
    doc.add_paragraph("Phần I: 1-A")
    doc.add_paragraph("Phần II: 1. a)Đúng b)Sai")
    doc.add_paragraph("Phần III: 1. 123")
    
    file_stream = io.BytesIO()
    doc.save(file_stream)
    file_stream.seek(0)
    return file_stream.getvalue()

def create_invalid_exam_missing_parts():
    """Tạo đề thi thiếu phần"""
    doc = Document()
    
    doc.add_paragraph("Đề thi môn Hóa học")
    doc.add_paragraph("")
    
    # Chỉ có PHẦN I, thiếu PHẦN II và III
    doc.add_paragraph("PHẦN I. Trắc nghiệm nhiều phương án")
    doc.add_paragraph("Câu 1. Câu hỏi test...")
    doc.add_paragraph("A. Đáp án A")
    doc.add_paragraph("")
    
    # Có đáp án
    doc.add_paragraph("ĐÁP ÁN")
    doc.add_paragraph("Phần I: 1-A")
    
    file_stream = io.BytesIO()
    doc.save(file_stream)
    file_stream.seek(0)
    return file_stream.getvalue()

def create_invalid_exam_missing_answers():
    """Tạo đề thi thiếu đáp án"""
    doc = Document()
    
    doc.add_paragraph("Đề thi môn Hóa học")
    doc.add_paragraph("")
    
    # Có đủ 3 phần
    doc.add_paragraph("PHẦN I. Trắc nghiệm nhiều phương án")
    doc.add_paragraph("Câu 1. Câu hỏi test...")
    doc.add_paragraph("")
    
    doc.add_paragraph("PHẦN II. Trắc nghiệm đúng sai")
    doc.add_paragraph("Câu 1. Câu hỏi test...")
    doc.add_paragraph("")
    
    doc.add_paragraph("PHẦN III. Trắc nghiệm trả lời ngắn")
    doc.add_paragraph("Câu 1. Câu hỏi test...")
    doc.add_paragraph("")
    
    # Thiếu phần ĐÁP ÁN
    
    file_stream = io.BytesIO()
    doc.save(file_stream)
    file_stream.seek(0)
    return file_stream.getvalue()

def test_case(name, docx_content, should_pass):
    """Test một trường hợp"""
    print(f"\n=== {name} ===")
    
    try:
        files = {
            'file': (f'{name.lower().replace(" ", "_")}.docx', docx_content, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
        }
        
        response = requests.post(
            "http://localhost:8000/api/v1/exam/import-docx",
            files=files,
            timeout=60
        )
        
        result = response.json()
        success = result.get("success", False)
        
        if should_pass:
            if success:
                print("✅ PASSED - Đề thi hợp lệ được chấp nhận")
                return True
            else:
                print(f"❌ FAILED - Đề thi hợp lệ bị từ chối: {result.get('error')}")
                return False
        else:
            if not success and result.get("error_code") == "INVALID_FORMAT":
                print(f"✅ PASSED - Đề thi không hợp lệ bị từ chối: {result.get('error')}")
                return True
            else:
                print(f"❌ FAILED - Đề thi không hợp lệ được chấp nhận")
                return False
                
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    """Chạy test"""
    print("🧪 Testing Simple Format Validation")
    print("=" * 50)
    
    # Test cases
    test_cases = [
        ("Valid Exam", create_valid_exam(), True),
        ("Missing Parts", create_invalid_exam_missing_parts(), False),
        ("Missing Answers", create_invalid_exam_missing_answers(), False)
    ]
    
    results = []
    for name, content, should_pass in test_cases:
        result = test_case(name, content, should_pass)
        results.append((name, result))
    
    # Kết quả
    print("\n" + "=" * 50)
    print("📊 RESULTS:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} {name}")
    
    print(f"\n📈 Summary: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
    else:
        print("⚠️ SOME TESTS FAILED!")

if __name__ == "__main__":
    main()
